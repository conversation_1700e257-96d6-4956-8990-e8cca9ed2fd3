import dotenv from 'dotenv';
dotenv.config({ path: `.env.${process.env.NODE_ENV || 'dev'}` });

const { private_key } =
  process.env.NODE_ENV === 'qa' && JSON.parse(process.env.DB_CERT || '{}');

const config = {
  dev: {
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'mysql'
  },
  qa: {
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'mysql',
    dialectOptions: {
      decimalNumbers: true,
      ssl: process.env.NODE_ENV === 'qa' && {
        ca: private_key
      }
    },
    pool: {
      max: 10, // Maximum number of connections in pool
      min: 2, // Minimum number of connections in pool
      acquire: 60000, // Maximum time (ms) to try getting connection before throwing error
      idle: 30000, // Maximum time (ms) a connection can be idle before being released
      evict: 1000, // Time interval (ms) to run eviction to free idle connections
      handleDisconnects: true // Automatically handle disconnects
    },
    language: 'en'
  },
  prod: {
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'mysql',
    pool: {
      max: 15, // Higher for production load
      min: 5, // Higher minimum for production
      acquire: 60000, // Maximum time (ms) to try getting connection before throwing error
      idle: 30000, // Maximum time (ms) a connection can be idle before being released
      evict: 1000, // Time interval (ms) to run eviction to free idle connections
      handleDisconnects: true // Automatically handle disconnects
    }
  }
};

export default config;
